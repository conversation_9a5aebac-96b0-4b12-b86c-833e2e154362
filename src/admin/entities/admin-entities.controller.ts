import { Controller, Patch, Param, Body, UseGuards, ParseUUIDPipe, Post, Query, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { SupabaseAuthGuard } from '../../auth/guards/supabase-auth.guard';
import { AdminGuard } from '../../auth/guards/admin.guard';
import { EntitiesService } from '../../entities/entities.service';
import { AdminUpdateEntityStatusDto } from './dto/admin-update-entity-status.dto';
import { Entity as EntityModel } from '../../../generated/prisma'; // Using Prisma model directly for now
// import { EntityResponseDto } from '../../entities/dto/entity-response.dto'; // Uncomment if you have this DTO

@ApiTags('Admin - Entities')
@Controller('admin/entities')
@UseGuards(SupabaseAuthGuard, AdminGuard)
@ApiBearerAuth() // Indicates that endpoints in this controller require a Bearer token
export class AdminEntitiesController {
  private readonly logger = new Logger(AdminEntitiesController.name);

  constructor(private readonly entitiesService: EntitiesService) {}

  @Patch(':id/status')
  @ApiOperation({ summary: 'Admin: Update entity status' })
  // @ApiResponse({ status: 200, description: 'Entity status updated successfully', type: EntityResponseDto }) // Uncomment if using EntityResponseDto
  // @ApiResponse({ status: 404, description: 'Entity not found' })
  // @ApiResponse({ status: 403, description: 'Forbidden - Admin role required' })
  // @ApiResponse({ status: 401, description: 'Unauthorized' })
  async updateEntityStatus(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() adminUpdateEntityStatusDto: AdminUpdateEntityStatusDto,
  ): Promise<EntityModel> { // Change to EntityResponseDto if you have it
    return this.entitiesService.adminSetStatus(id, adminUpdateEntityStatusDto.status);
  }

  @Post('backfill-embeddings')
  @ApiOperation({
    summary: 'Admin: Backfill missing embeddings',
    description: 'Generate embeddings for entities that don\'t have them. This is useful for fixing entities created before the embedding system was implemented.'
  })
  @ApiQuery({
    name: 'batchSize',
    required: false,
    type: Number,
    description: 'Number of entities to process in this batch (default: 10)',
    example: 10
  })
  @ApiResponse({
    status: 200,
    description: 'Backfill process completed',
    schema: {
      type: 'object',
      properties: {
        processed: { type: 'number', description: 'Number of entities successfully processed' },
        failed: {
          type: 'array',
          items: { type: 'string' },
          description: 'List of entities that failed to process with error messages'
        },
        message: { type: 'string', description: 'Summary message' }
      }
    }
  })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin role required' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async backfillMissingEmbeddings(
    @Query('batchSize') batchSize?: number,
  ): Promise<{ processed: number; failed: string[]; message: string }> {
    this.logger.log(`[Admin] Starting embedding backfill with batch size: ${batchSize || 10}`);

    const result = await this.entitiesService.backfillMissingEmbeddings(batchSize || 10);

    const message = `Backfill completed: ${result.processed} entities processed successfully, ${result.failed.length} failed`;
    this.logger.log(`[Admin] ${message}`);

    if (result.failed.length > 0) {
      this.logger.warn(`[Admin] Failed entities:`, result.failed);
    }

    return {
      ...result,
      message
    };
  }
}